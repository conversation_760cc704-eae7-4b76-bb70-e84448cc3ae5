import React, { memo, useState, useCallback, useMemo, lazy, Suspense, useEffect } from 'react';
import { ChatMessage } from '../../contexts/ChatContext';
import { PERFORMANCE_CONFIG } from '../../config/performance';

// Lazy load ReactMarkdown for better performance
const ReactMarkdown = lazy(() => import('react-markdown'));

interface OptimizedMessageBubbleProps {
  message: ChatMessage;
}

// Memoized code block component
const CodeBlock = memo(({ children, className, isUser }: { 
  children: React.ReactNode; 
  className?: string; 
  isUser: boolean;
}) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(String(children));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  }, [children]);

  const isInline = !className?.includes('language-');

  if (isInline) {
    return (
      <code
        className={`px-2 py-1 rounded text-sm font-mono break-all ${
          isUser ? 'bg-blue-400 bg-opacity-30 text-white' : 'bg-gray-100 text-gray-800'
        }`}
      >
        {children}
      </code>
    );
  }

  return (
    <div className="relative group my-4">
      <div className="overflow-hidden rounded border border-gray-100">
        <div className="flex items-center justify-between px-3 py-2 bg-gray-50 border-b border-gray-100">
          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
            {className?.replace('language-', '') || 'code'}
          </span>
          <button
            onClick={handleCopy}
            className={`px-2 py-1 rounded text-xs font-medium transition-all duration-200 ${
              copied
                ? 'bg-green-100 text-green-600'
                : 'bg-white text-gray-500 hover:text-gray-700 hover:bg-gray-100'
            }`}
            title={copied ? 'Copiado!' : 'Copiar'}
          >
            {copied ? '✓ Copiado' : 'Copiar'}
          </button>
        </div>
        <pre className="p-4 bg-gray-900 text-gray-100 overflow-x-auto text-sm">
          <code>{children}</code>
        </pre>
      </div>
    </div>
  );
});

// Memoized markdown content component
const MarkdownContent = memo(({ content, isUser }: { content: string; isUser: boolean }) => {
  const [remarkGfmPlugin, setRemarkGfmPlugin] = useState<typeof import('remark-gfm').default | null>(null);

  // Load remark-gfm plugin lazily
  React.useEffect(() => {
    import('remark-gfm').then(module => {
      setRemarkGfmPlugin(module.default);
    });
  }, []);

  const markdownComponents = useMemo(() => ({
    code: ({ className, children }: React.ComponentProps<'code'>) => (
      <CodeBlock className={className} isUser={isUser}>
        {children}
      </CodeBlock>
    ),
    a: ({ children, href, ...props }: React.ComponentProps<'a'>) => (
      <a
        href={href}
        className={`underline ${isUser ? 'text-blue-100 hover:text-white' : 'text-indigo-600 hover:text-indigo-800'}`}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
      >
        {children}
      </a>
    ),
    strong: ({ children, ...props }: React.ComponentProps<'strong'>) => (
      <strong className={`font-semibold ${isUser ? 'text-white' : 'text-gray-900'}`} {...props}>
        {children}
      </strong>
    ),
    em: ({ children, ...props }: React.ComponentProps<'em'>) => (
      <em className={`italic ${isUser ? 'text-white' : 'text-gray-700'}`} {...props}>
        {children}
      </em>
    ),
  }), [isUser]);

  return (
    <Suspense fallback={<div className="animate-pulse bg-gray-200 h-4 rounded"></div>}>
      <ReactMarkdown
        remarkPlugins={remarkGfmPlugin ? [remarkGfmPlugin] : []}
        components={markdownComponents}
      >
        {content}
      </ReactMarkdown>
    </Suspense>
  );
});

// Progressive content renderer for very long messages
const ProgressiveContentRenderer = memo(({ content, isUser }: { content: string; isUser: boolean }) => {
  const [displayedContent, setDisplayedContent] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);

  // Configuration for progressive rendering from performance config
  const INITIAL_CHUNK_SIZE = PERFORMANCE_CONFIG.LARGE_MESSAGE.INITIAL_CHUNK_SIZE;
  const CHUNK_SIZE = PERFORMANCE_CONFIG.LARGE_MESSAGE.PROGRESSIVE_CHUNK_SIZE;
  const VERY_LONG_THRESHOLD = PERFORMANCE_CONFIG.LARGE_MESSAGE.VERY_LONG_THRESHOLD;
  const RENDER_DELAY = PERFORMANCE_CONFIG.LARGE_MESSAGE.RENDER_DELAY;

  const isVeryLong = content.length > VERY_LONG_THRESHOLD;
  const needsTruncation = content.length > INITIAL_CHUNK_SIZE;

  useEffect(() => {
    if (!needsTruncation) {
      setDisplayedContent(content);
      return;
    }

    // Show initial chunk immediately
    setDisplayedContent(content.substring(0, INITIAL_CHUNK_SIZE));
  }, [content, needsTruncation, INITIAL_CHUNK_SIZE]);

  const expandContent = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    setProgress(0);

    const totalLength = content.length;
    let currentLength = displayedContent.length;

    // Progressive loading with chunks
    while (currentLength < totalLength) {
      const nextChunkEnd = Math.min(currentLength + CHUNK_SIZE, totalLength);
      const newContent = content.substring(0, nextChunkEnd);

      // Use requestIdleCallback to avoid blocking the UI
      await new Promise(resolve => {
        const callback = () => {
          setDisplayedContent(newContent);
          setProgress((nextChunkEnd / totalLength) * 100);
          currentLength = nextChunkEnd;
          resolve(void 0);
        };

        if ('requestIdleCallback' in window) {
          requestIdleCallback(callback);
        } else {
          setTimeout(callback, 0);
        }
      });

      // Small delay to prevent UI blocking
      await new Promise(resolve => setTimeout(resolve, RENDER_DELAY));
    }

    setIsExpanded(true);
    setIsLoading(false);
    setProgress(100);
  }, [content, displayedContent.length, CHUNK_SIZE, isLoading]);

  const collapseContent = useCallback(() => {
    setDisplayedContent(content.substring(0, INITIAL_CHUNK_SIZE));
    setIsExpanded(false);
    setProgress(0);
  }, [content, INITIAL_CHUNK_SIZE]);

  return (
    <div>
      <MarkdownContent content={displayedContent} isUser={isUser} />

      {needsTruncation && (
        <div className="mt-4 space-y-2">
          {/* Progress bar for very long content */}
          {isLoading && isVeryLong && (
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          )}

          {/* Expand/Collapse buttons */}
          {!isExpanded ? (
            <button
              onClick={expandContent}
              disabled={isLoading}
              className={`inline-flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                isUser
                  ? 'bg-blue-400 bg-opacity-30 text-white hover:bg-opacity-50'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                  <span>Cargando... ({Math.round(progress)}%)</span>
                </>
              ) : (
                <>
                  <span>📄</span>
                  <span>
                    Ver mensaje completo ({Math.round((content.length - INITIAL_CHUNK_SIZE) / 1000)}k caracteres más)
                  </span>
                </>
              )}
            </button>
          ) : (
            <button
              onClick={collapseContent}
              className={`inline-flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                isUser
                  ? 'bg-blue-400 bg-opacity-30 text-white hover:bg-opacity-50'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <span>📄</span>
              <span>Contraer mensaje</span>
            </button>
          )}

          {/* Content stats for very long messages */}
          {isVeryLong && (
            <div className={`text-xs ${isUser ? 'text-blue-100' : 'text-gray-500'}`}>
              Mensaje largo: {Math.round(content.length / 1000)}k caracteres
              {isExpanded && ' (completamente cargado)'}
            </div>
          )}
        </div>
      )}
    </div>
  );
});

// Main optimized message bubble component
const OptimizedMessageBubble: React.FC<OptimizedMessageBubbleProps> = memo(({ message }) => {
  const [isStepsOpen, setIsStepsOpen] = useState(false);

  const isUser = message.from_sender === 'User';
  const hasIntermediateSteps = message.intermediate_steps && message.intermediate_steps.length > 0;

  // Basic content validation
  const messageContent = useMemo(() => {
    return message.content || '';
  }, [message.content]);

  const toggleSteps = useCallback(() => {
    setIsStepsOpen(prev => !prev);
  }, []);

  return (
    <div className={`w-full flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div
        className={`relative w-full ${isUser ? 'max-w-4xl' : 'max-w-6xl'} px-6 py-5 rounded-2xl shadow-lg overflow-hidden ${
          isUser
            ? 'bg-gradient-to-br from-blue-500 to-blue-600 text-white'
            : 'bg-white text-gray-800 border border-gray-100'
        }`}
      >
        {/* Message content with progressive rendering */}
        <div className={`prose prose-lg max-w-none break-words overflow-hidden ${!isUser && message.type === 'answer' ? 'pt-6' : ''}`}>
          {messageContent ? (
            <ProgressiveContentRenderer content={messageContent} isUser={isUser} />
          ) : (
            <div className="text-gray-500 italic">Sin contenido</div>
          )}
        </div>

        {/* Intermediate steps (collapsed by default for performance) */}
        {hasIntermediateSteps && (
          <div className="mt-4 border-t border-gray-200 pt-4">
            <button
              onClick={toggleSteps}
              className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
              aria-expanded={isStepsOpen}
            >
              <span>🔍</span>
              <span>Ver pasos intermedios ({message.intermediate_steps?.length})</span>
              <svg
                className={`w-4 h-4 transition-transform ${isStepsOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Lazy load intermediate steps only when opened */}
            {isStepsOpen && (
              <div className="mt-2 space-y-2 text-xs max-h-96 overflow-y-auto">
                {message.intermediate_steps?.map((step, index) => (
                  <div key={`step-${index}-${step.created_at}`} className="p-2 bg-gray-50 rounded border">
                    <div className="font-medium text-gray-700 mb-1">
                      Paso {index + 1}: {step.type}
                    </div>
                    <div className="text-gray-600 text-xs">
                      {step.content ? (
                        step.content.length > 500 
                          ? step.content.substring(0, 500) + '...'
                          : step.content
                      ) : 'Sin contenido'}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Timestamp */}
        <div className={`text-xs mt-3 ${isUser ? 'text-blue-100' : 'text-gray-500'}`}>
          {new Date(message.created_at).toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
});

OptimizedMessageBubble.displayName = 'OptimizedMessageBubble';

export default OptimizedMessageBubble;
