import React, { useState } from 'react';
import OptimizedMessageBubble from './OptimizedMessageBubble';
import { ChatMessage } from '../../contexts/ChatContext';

const LargeMessageTest: React.FC = () => {
  const [testMessage, setTestMessage] = useState<ChatMessage | null>(null);

  const generateLargeMessage = (size: 'small' | 'medium' | 'large' | 'extreme') => {
    let content = '';
    let targetLength = 0;

    switch (size) {
      case 'small':
        targetLength = 5000; // 5k characters
        break;
      case 'medium':
        targetLength = 25000; // 25k characters
        break;
      case 'large':
        targetLength = 75000; // 75k characters
        break;
      case 'extreme':
        targetLength = 150000; // 150k characters (simulating 100k+ tokens)
        break;
    }

    // Generate realistic content with markdown
    const paragraphs = [
      "# Análisis Detallado de Optimización de Rendimiento\n\n",
      "## Introducción\n\nEste documento presenta un análisis exhaustivo de las técnicas de optimización implementadas para mejorar el rendimiento de la aplicación de chat cuando se manejan mensajes extremadamente largos.\n\n",
      "### Problemas Identificados\n\n1. **Bloqueo del hilo principal**: Cuando se renderizaban mensajes de más de 100k tokens, el navegador se congelaba debido a la carga sincrónica de todo el contenido.\n\n2. **Consumo excesivo de memoria**: El renderizado de todos los mensajes simultáneamente causaba picos de memoria que afectaban la experiencia del usuario.\n\n3. **Tiempo de respuesta lento**: La interfaz se volvía no responsiva durante el procesamiento de contenido largo.\n\n",
      "### Soluciones Implementadas\n\n#### 1. Virtualización de Chat History\n\nSe reemplazó el componente `ChatHistory` básico por `VirtualChatHistory` que implementa:\n\n- **Renderizado virtual**: Solo se renderizan los mensajes visibles en pantalla\n- **Paginación inteligente**: Carga de mensajes por chunks para evitar sobrecarga\n- **Gestión de memoria**: Liberación automática de componentes no visibles\n\n```typescript\n// Ejemplo de configuración de virtualización\nconst VIRTUAL_CONFIG = {\n  itemHeight: 'auto',\n  overscan: 5,\n  scrollBehavior: 'smooth'\n};\n```\n\n#### 2. Renderizado Progresivo de Mensajes\n\nImplementación de `ProgressiveContentRenderer` con las siguientes características:\n\n- **Carga inicial limitada**: Solo se muestran los primeros 2000 caracteres\n- **Expansión por chunks**: El contenido adicional se carga en fragmentos de 5000 caracteres\n- **Renderizado asíncrono**: Uso de `requestIdleCallback` para evitar bloqueo del UI\n- **Indicadores de progreso**: Barras de progreso para mensajes muy largos\n\n```typescript\n// Configuración de renderizado progresivo\nconst PROGRESSIVE_CONFIG = {\n  INITIAL_CHUNK_SIZE: 2000,\n  CHUNK_SIZE: 5000,\n  VERY_LONG_THRESHOLD: 50000,\n  RENDER_DELAY: 10\n};\n```\n\n#### 3. Optimización de Componentes\n\n- **Memoización**: Uso extensivo de `React.memo` y `useMemo`\n- **Lazy loading**: Carga diferida de componentes pesados como ReactMarkdown\n- **Cache de componentes**: Sistema de cache para evitar re-renderizados innecesarios\n\n",
      "### Métricas de Rendimiento\n\n#### Antes de la Optimización\n- Tiempo de renderizado: >5000ms para mensajes de 100k tokens\n- Uso de memoria: Picos de >200MB\n- Bloqueo de UI: 3-8 segundos\n- Experiencia de usuario: Inaceptable\n\n#### Después de la Optimización\n- Tiempo de renderizado inicial: <100ms\n- Uso de memoria: Estable <50MB\n- Bloqueo de UI: Eliminado\n- Experiencia de usuario: Fluida y responsiva\n\n",
      "### Casos de Uso Soportados\n\n1. **Documentos largos**: Análisis de documentos de hasta 200k caracteres\n2. **Código extenso**: Revisión de bases de código completas\n3. **Reportes detallados**: Generación de informes exhaustivos\n4. **Transcripciones**: Procesamiento de transcripciones de reuniones largas\n5. **Análisis de datos**: Presentación de datasets complejos\n\n",
      "### Configuración Técnica\n\n#### Performance Config\n```typescript\nexport const PERFORMANCE_CONFIG = {\n  LARGE_MESSAGE: {\n    INITIAL_CHUNK_SIZE: 2000,\n    PROGRESSIVE_CHUNK_SIZE: 5000,\n    VERY_LONG_THRESHOLD: 50000,\n    EXTREME_LONG_THRESHOLD: 100000,\n    RENDER_DELAY: 10,\n    MAX_IMMEDIATE_RENDER: 10000,\n  },\n  VIRTUAL_SCROLL: {\n    ITEM_HEIGHT: 200,\n    OVERSCAN: 5,\n    BUFFER_SIZE: 10,\n  }\n};\n```\n\n#### Thresholds de Rendimiento\n- **Bajo impacto**: <10k tokens - Renderizado normal\n- **Impacto medio**: 10k-50k tokens - Virtualización activada\n- **Alto impacto**: 50k-100k tokens - Renderizado progresivo\n- **Impacto crítico**: >100k tokens - Optimizaciones agresivas\n\n",
      "### Monitoreo y Métricas\n\nSistema de monitoreo en tiempo real que incluye:\n\n- **Uso de memoria**: Tracking de `usedJSHeapSize`\n- **Tiempo de renderizado**: Medición de performance por componente\n- **Cache hit ratio**: Eficiencia del sistema de cache\n- **Scroll performance**: FPS durante el desplazamiento\n\n",
      "### Conclusiones\n\nLas optimizaciones implementadas han transformado completamente la experiencia de usuario cuando se trabaja con mensajes extremadamente largos. El sistema ahora puede manejar:\n\n- ✅ Mensajes de hasta 200k caracteres sin bloqueo\n- ✅ Renderizado fluido y responsivo\n- ✅ Uso eficiente de memoria\n- ✅ Experiencia de usuario consistente\n- ✅ Escalabilidad para futuras mejoras\n\n### Próximos Pasos\n\n1. **Implementar streaming**: Renderizado en tiempo real para mensajes que se generan dinámicamente\n2. **Optimizar markdown**: Procesamiento asíncrono de markdown complejo\n3. **Cache persistente**: Almacenamiento local de contenido procesado\n4. **Métricas avanzadas**: Dashboard de rendimiento en tiempo real\n\n"
    ];

    // Repeat content to reach target length
    while (content.length < targetLength) {
      for (const paragraph of paragraphs) {
        content += paragraph;
        if (content.length >= targetLength) break;
      }
    }

    // Trim to exact length
    content = content.substring(0, targetLength);

    const message: ChatMessage = {
      message_id: `test-${Date.now()}`,
      thread_id: 'test-thread',
      content,
      from_sender: 'Assistant',
      type: 'answer',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      intermediate_steps: []
    };

    setTestMessage(message);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold mb-4">Prueba de Mensajes Largos</h2>
        <p className="text-gray-600 mb-4">
          Utiliza estos botones para probar el renderizado progresivo con diferentes tamaños de mensaje:
        </p>
        
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => generateLargeMessage('small')}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Pequeño (5k chars)
          </button>
          <button
            onClick={() => generateLargeMessage('medium')}
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 transition-colors"
          >
            Mediano (25k chars)
          </button>
          <button
            onClick={() => generateLargeMessage('large')}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors"
          >
            Grande (75k chars)
          </button>
          <button
            onClick={() => generateLargeMessage('extreme')}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
          >
            Extremo (150k chars)
          </button>
          <button
            onClick={() => setTestMessage(null)}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            Limpiar
          </button>
        </div>
      </div>

      {testMessage && (
        <div className="border rounded-lg p-4 bg-gray-50">
          <div className="mb-4 text-sm text-gray-600">
            <strong>Tamaño del mensaje:</strong> {testMessage.content.length.toLocaleString()} caracteres
            <br />
            <strong>Tokens estimados:</strong> {Math.round(testMessage.content.length / 4).toLocaleString()}
          </div>
          <OptimizedMessageBubble message={testMessage} />
        </div>
      )}
    </div>
  );
};

export default LargeMessageTest;
